QT       += core gui network

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++11

# You can make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    main.cpp \
    mainwindow.cpp

HEADERS += \
    mainwindow.h

FORMS += \
    mainwindow.ui

# FFmpeg库配置 - 使用系统安装的FFmpeg库
# 使用pkg-config获取FFmpeg编译选项
CONFIG += link_pkgconfig
PKGCONFIG += libavformat libavcodec libavutil libswscale libavfilter libavdevice

# 额外的系统库依赖
LIBS += -lz -lm -lpthread -ldl

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target
