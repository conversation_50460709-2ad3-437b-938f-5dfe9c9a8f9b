#!/bin/bash
BOARD_IP="*************"  # 请根据实际IP修改
BOARD_USER="root"

echo "=== 测试开发板连接 ==="
echo "尝试连接到 $BOARD_USER@$BOARD_IP"

# 测试SSH连接
if timeout 10 ssh -o ConnectTimeout=5 -o StrictHostKeyChecking=no $BOARD_USER@$BOARD_IP "echo 'SSH连接成功'" 2>/dev/null; then
    echo "✅ SSH连接正常"
    
    # 测试NFS挂载
    echo "测试NFS挂载..."
    ssh -o StrictHostKeyChecking=no $BOARD_USER@$BOARD_IP "
        mkdir -p /mnt/nfs
        if mount -t nfs $(hostname -I | awk '{print $1}'):/home/<USER>/nfs_root /mnt/nfs 2>/dev/null; then
            echo '✅ NFS挂载成功'
            ls -la /mnt/nfs/s5p6818-app/
            umount /mnt/nfs
        else
            echo '❌ NFS挂载失败'
        fi
    "
else
    echo "❌ 无法连接到开发板"
    echo "请检查："
    echo "1. 开发板IP地址是否正确"
    echo "2. 网络连接是否正常"
    echo "3. SSH服务是否启动"
fi
