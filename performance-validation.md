# s5p6818开发板Qt RTSP播放器性能和稳定性验证

## 验证概述

本文档提供了在s5p6818开发板上验证Qt RTSP视频流播放器性能和稳定性的详细指南。

## 技术规格

### 硬件平台
- **开发板**: s5p6818 (ARM Cortex-A53 八核)
- **显示屏**: 1024×600 触摸屏
- **内存**: 1GB DDR3
- **存储**: eMMC/SD卡

### 软件环境
- **Qt版本**: 5.15.3 (ARM交叉编译)
- **FFmpeg版本**: 5.0.1 (ARM交叉编译)
- **操作系统**: Linux (ARM)
- **编译工具链**: arm-linux-gnueabihf-gcc

## 验证项目

### 1. 基础功能验证

#### 1.1 应用程序启动
```bash
# 在开发板上执行
/home/<USER>/s5p6818-app/start.sh
```

**验证要点**:
- [ ] 应用程序能够正常启动
- [ ] Qt界面正确显示在1024×600屏幕上
- [ ] 触摸功能响应正常
- [ ] 无明显的启动错误或警告

#### 1.2 RTSP连接测试
**测试用例**:
```
RTSP URL: rtsp://example.com/stream
预期结果: 成功连接并显示视频流
```

**验证要点**:
- [ ] RTSP连接建立成功
- [ ] 视频流解码正常
- [ ] 画面显示流畅
- [ ] 音频同步正常（如果有音频）

#### 1.3 拍照功能测试
**验证要点**:
- [ ] 拍照按钮响应正常
- [ ] 图片保存成功
- [ ] 图片质量符合预期
- [ ] 文件命名规则正确

#### 1.4 录像功能测试
**验证要点**:
- [ ] 录像开始/停止功能正常
- [ ] 视频文件保存成功
- [ ] 录像质量符合预期
- [ ] 文件格式正确

### 2. 性能验证

#### 2.1 CPU使用率监控
```bash
# 在开发板上监控CPU使用率
top -p $(pgrep s5p6818-qt)
```

**性能指标**:
- **空闲状态**: CPU使用率 < 10%
- **RTSP播放**: CPU使用率 < 60%
- **录像状态**: CPU使用率 < 80%

#### 2.2 内存使用监控
```bash
# 监控内存使用
cat /proc/$(pgrep s5p6818-qt)/status | grep -E "(VmRSS|VmSize)"
```

**性能指标**:
- **启动后**: 内存使用 < 100MB
- **播放状态**: 内存使用 < 150MB
- **长时间运行**: 无明显内存泄漏

#### 2.3 视频播放性能
**测试参数**:
- **分辨率**: 1920×1080, 1280×720, 640×480
- **帧率**: 30fps, 25fps, 15fps
- **码率**: 2Mbps, 1Mbps, 512Kbps

**性能指标**:
- **1080p@30fps**: 流畅播放，CPU < 70%
- **720p@30fps**: 流畅播放，CPU < 50%
- **480p@30fps**: 流畅播放，CPU < 30%

### 3. 稳定性验证

#### 3.1 长时间运行测试
**测试方案**:
- 连续播放RTSP流24小时
- 每小时记录系统状态
- 监控内存泄漏和CPU异常

**验证要点**:
- [ ] 应用程序无崩溃
- [ ] 视频播放无中断
- [ ] 内存使用稳定
- [ ] 系统响应正常

#### 3.2 网络异常处理
**测试场景**:
- 网络断开重连
- RTSP服务器重启
- 网络延迟和丢包

**验证要点**:
- [ ] 网络异常时有适当提示
- [ ] 网络恢复后自动重连
- [ ] 异常处理不导致程序崩溃

#### 3.3 触摸操作压力测试
**测试方案**:
- 快速连续点击各个按钮
- 多点触摸测试
- 长按操作测试

**验证要点**:
- [ ] 界面响应及时
- [ ] 无按钮失效现象
- [ ] 触摸精度准确

### 4. 兼容性验证

#### 4.1 不同RTSP源测试
**测试源**:
- IP摄像头 (H.264/H.265)
- 流媒体服务器 (FFmpeg, VLC)
- 网络摄像头 (USB转RTSP)

#### 4.2 不同网络环境
**测试环境**:
- 有线网络 (100Mbps)
- WiFi网络 (2.4GHz/5GHz)
- 移动热点

## 性能优化建议

### 1. 系统级优化
```bash
# 设置CPU调频策略
echo performance > /sys/devices/system/cpu/cpu0/cpufreq/scaling_governor

# 调整网络缓冲区
echo 'net.core.rmem_max = 16777216' >> /etc/sysctl.conf
echo 'net.core.wmem_max = 16777216' >> /etc/sysctl.conf
```

### 2. 应用级优化
- 启用FFmpeg硬件加速
- 优化Qt渲染设置
- 调整缓冲区大小

### 3. 显示优化
```bash
# 设置显示环境变量
export QT_QPA_PLATFORM=linuxfb
export QT_QPA_FB_DRM=1
export QT_QPA_FONTDIR=/usr/share/fonts
```

## 故障排除

### 常见问题及解决方案

1. **应用程序无法启动**
   - 检查库依赖: `ldd s5p6818-qt-glibc218-compatible-arm`
   - 验证权限设置: `chmod +x s5p6818-qt-glibc218-compatible-arm`

2. **RTSP连接失败**
   - 检查网络连接: `ping rtsp-server-ip`
   - 验证RTSP URL格式
   - 检查防火墙设置

3. **视频播放卡顿**
   - 降低视频分辨率或码率
   - 检查网络带宽
   - 监控CPU使用率

4. **触摸不响应**
   - 检查触摸屏驱动
   - 验证Qt触摸配置
   - 重新校准触摸屏

## 验证报告模板

### 测试环境
- 开发板型号: s5p6818
- 软件版本: Qt 5.15.3 + FFmpeg 5.0.1
- 测试日期: ____
- 测试人员: ____

### 测试结果
- [ ] 基础功能验证通过
- [ ] 性能指标达标
- [ ] 稳定性测试通过
- [ ] 兼容性验证通过

### 问题记录
| 问题描述 | 严重程度 | 解决方案 | 状态 |
|---------|---------|---------|------|
|         |         |         |      |

### 总体评价
- 功能完整性: ___/10
- 性能表现: ___/10
- 稳定性: ___/10
- 用户体验: ___/10

**总分**: ___/40
