#!/bin/bash

echo "=== s5p6818 ARM交叉编译脚本 ==="

# 清理之前的编译文件
echo "清理编译文件..."
make clean 2>/dev/null || true
rm -f Makefile .qmake.stash

# 生成Makefile
echo "生成Makefile..."
qmake CONFIG+=arm_cross_compile CameraStreamTest.pro

if [ ! -f Makefile ]; then
    echo "错误：无法生成Makefile"
    exit 1
fi

# 修复Makefile中的x86_64库路径
echo "修复Makefile中的库路径..."
sed -i 's|/usr/lib/x86_64-linux-gnu/libQt5[^ ]*.so||g' Makefile
sed -i 's|-lGL||g' Makefile

# 检查修复后的库链接
echo "检查修复后的库链接："
grep "LIBS.*=" Makefile | head -1

# 开始编译
echo "开始ARM交叉编译..."
make -j$(nproc)

if [ $? -eq 0 ]; then
    echo "=== 编译成功 ==="
    echo "生成的ARM可执行文件："
    ls -la s5p6818-qt-glibc218-compatible-arm
    echo ""
    echo "检查文件架构："
    file s5p6818-qt-glibc218-compatible-arm
    echo ""
    echo "检查库依赖："
    arm-linux-gnueabihf-readelf -d s5p6818-qt-glibc218-compatible-arm | grep NEEDED | head -10
else
    echo "=== 编译失败 ==="
    exit 1
fi
