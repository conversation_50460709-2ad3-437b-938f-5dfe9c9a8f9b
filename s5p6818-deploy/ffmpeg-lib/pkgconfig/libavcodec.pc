prefix=/home/<USER>/arm-cross-compile/install/ffmpeg-arm
exec_prefix=${prefix}
libdir=/home/<USER>/arm-cross-compile/install/ffmpeg-arm/lib
includedir=/home/<USER>/arm-cross-compile/install/ffmpeg-arm/include

Name: libavcodec
Description: FFmpeg codec library
Version: 59.18.100
Requires: 
Requires.private: libswresample >= 4.3.100, libavutil >= 57.17.100
Conflicts:
Libs: -L${libdir}  -lavcodec 
Libs.private: -pthread -lm -latomic
Cflags: -I${includedir}
