prefix=/home/<USER>/arm-cross-compile/install/ffmpeg-arm
exec_prefix=${prefix}
libdir=/home/<USER>/arm-cross-compile/install/ffmpeg-arm/lib
includedir=/home/<USER>/arm-cross-compile/install/ffmpeg-arm/include

Name: libavdevice
Description: FFmpeg device handling library
Version: 59.4.100
Requires: 
Requires.private: libavfilter >= 8.24.100, libswscale >= 6.4.100, libpostproc >= 56.3.100, libavformat >= 59.16.100, libavcodec >= 59.18.100, libswresample >= 4.3.100, libavutil >= 57.17.100
Conflicts:
Libs: -L${libdir}  -lavdevice 
Libs.private: -lm -latomic -L/usr/lib/arm-linux-gnueabihf -lxcb
Cflags: -I${includedir}
