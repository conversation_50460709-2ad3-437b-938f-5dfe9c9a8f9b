#!/bin/sh
# 紧急启动脚本 - 完全从NFS运行，适用于存储空间极度有限的情况

echo "=== s5p6818紧急启动脚本 ==="
echo "完全从NFS运行，无需本地存储空间"

# 检查NFS挂载
if [ ! -d /nfs/s5p6818-app ]; then
    echo "错误: NFS应用目录不存在"
    exit 1
fi

# 设置环境变量
echo "设置环境变量..."
export LD_LIBRARY_PATH=/nfs/s5p6818-app/ffmpeg-lib:$LD_LIBRARY_PATH
export QT_QPA_PLATFORM=linuxfb
export QT_QPA_FB_DRM=1

# 显示环境信息
echo "当前环境:"
echo "LD_LIBRARY_PATH: $LD_LIBRARY_PATH"
echo "QT_QPA_PLATFORM: $QT_QPA_PLATFORM"

# 检查显示设备
echo "检查显示设备..."
if [ -e /dev/fb0 ]; then
    echo "✓ 找到帧缓冲设备: /dev/fb0"
else
    echo "⚠ 警告: 未找到帧缓冲设备"
fi

# 检查应用程序
echo "检查应用程序..."
if [ -f /nfs/s5p6818-app/bin/s5p6818-qt-glibc218-compatible-arm ]; then
    echo "✓ 应用程序存在"
    ls -lh /nfs/s5p6818-app/bin/s5p6818-qt-glibc218-compatible-arm
else
    echo "✗ 应用程序不存在"
    exit 1
fi

# 检查FFmpeg库
echo "检查FFmpeg库..."
if [ -d /nfs/s5p6818-app/ffmpeg-lib ]; then
    echo "✓ FFmpeg库目录存在"
    echo "库文件数量: $(ls /nfs/s5p6818-app/ffmpeg-lib/*.so.* 2>/dev/null | wc -l)"
else
    echo "✗ FFmpeg库目录不存在"
    exit 1
fi

# 检查库依赖
echo "检查库依赖..."
if command -v ldd >/dev/null 2>&1; then
    echo "库依赖检查:"
    ldd /nfs/s5p6818-app/bin/s5p6818-qt-glibc218-compatible-arm | head -5
else
    echo "ldd命令不可用，跳过依赖检查"
fi

# 启动应用程序
echo "启动Qt RTSP播放器..."
cd /nfs/s5p6818-app/bin

# 尝试启动
echo "执行: ./s5p6818-qt-glibc218-compatible-arm"
exec ./s5p6818-qt-glibc218-compatible-arm
