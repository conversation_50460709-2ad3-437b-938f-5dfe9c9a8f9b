#!/bin/sh
# 适用于s5p6818开发板的部署脚本 (Buildroot 2015.05 + busybox)

echo "=== s5p6818开发板部署脚本 ==="

# 设置目标路径
TARGET_DIR="/home/<USER>/s5p6818-app"
FFMPEG_LIB_DIR="/usr/local/lib/ffmpeg"

echo "创建目标目录..."
mkdir -p $TARGET_DIR
mkdir -p $FFMPEG_LIB_DIR

echo "复制应用程序..."
cp bin/s5p6818-qt-glibc218-compatible-arm $TARGET_DIR/
chmod +x $TARGET_DIR/s5p6818-qt-glibc218-compatible-arm

echo "复制FFmpeg库..."
cp ffmpeg-lib/* $FFMPEG_LIB_DIR/

echo "更新库缓存..."
ldconfig 2>/dev/null || echo "ldconfig不可用，跳过库缓存更新"

echo "创建启动脚本..."
cat > $TARGET_DIR/start.sh << 'STARTEOF'
#!/bin/sh
echo "=== 启动s5p6818 Qt RTSP播放器 ==="

# 设置库路径
export LD_LIBRARY_PATH=/usr/local/lib/ffmpeg:/usr/lib/arm-linux-gnueabihf:$LD_LIBRARY_PATH

# 设置Qt显示环境 (适用于Buildroot)
export QT_QPA_PLATFORM=linuxfb
export QT_QPA_FB_DRM=1

# 检查显示设备
if [ -e /dev/fb0 ]; then
    echo "找到帧缓冲设备: /dev/fb0"
else
    echo "警告: 未找到帧缓冲设备"
fi

# 切换到应用目录
cd /home/<USER>/s5p6818-app

# 检查应用程序
if [ -x ./s5p6818-qt-glibc218-compatible-arm ]; then
    echo "启动Qt应用程序..."
    ./s5p6818-qt-glibc218-compatible-arm
else
    echo "错误: 应用程序不存在或无执行权限"
    exit 1
fi
STARTEOF

chmod +x $TARGET_DIR/start.sh

echo "创建测试脚本..."
cat > $TARGET_DIR/test-env.sh << 'TESTEOF'
#!/bin/sh
echo "=== s5p6818环境测试 ==="

echo "1. 系统信息:"
uname -a

echo "2. 应用程序信息:"
if [ -f /home/<USER>/s5p6818-app/s5p6818-qt-glibc218-compatible-arm ]; then
    file /home/<USER>/s5p6818-app/s5p6818-qt-glibc218-compatible-arm
    echo "文件大小: $(ls -lh /home/<USER>/s5p6818-app/s5p6818-qt-glibc218-compatible-arm | awk '{print $5}')"
else
    echo "应用程序文件不存在"
fi

echo "3. 库依赖检查:"
if command -v ldd >/dev/null 2>&1; then
    ldd /home/<USER>/s5p6818-app/s5p6818-qt-glibc218-compatible-arm | head -10
else
    echo "ldd命令不可用"
fi

echo "4. FFmpeg库检查:"
ls -la /usr/local/lib/ffmpeg/*.so.* 2>/dev/null | head -5 || echo "FFmpeg库未找到"

echo "5. 显示设备检查:"
ls -la /dev/fb* 2>/dev/null || echo "未找到帧缓冲设备"

echo "6. 内存信息:"
free 2>/dev/null || cat /proc/meminfo | head -3

echo "7. 库路径检查:"
echo "LD_LIBRARY_PATH: $LD_LIBRARY_PATH"
TESTEOF

chmod +x $TARGET_DIR/test-env.sh

echo "部署完成！"
echo "测试命令: /home/<USER>/s5p6818-app/test-env.sh"
echo "启动命令: /home/<USER>/s5p6818-app/start.sh"
