#!/bin/sh
# 直接从NFS运行的部署脚本 - 适用于存储空间有限的s5p6818开发板

echo "=== s5p6818开发板NFS直接运行部署脚本 ==="

# 检查磁盘空间
echo "=== 检查磁盘空间 ==="
if command -v df >/dev/null 2>&1; then
    df -h
else
    # busybox可能没有df，使用替代方法
    cat /proc/mounts
    echo "检查根分区剩余空间:"
    busybox df / 2>/dev/null || echo "无法检查磁盘空间"
fi

# 检查当前NFS挂载点
NFS_APP_DIR="/nfs/s5p6818-app"
if [ ! -d "$NFS_APP_DIR" ]; then
    echo "错误: NFS应用目录不存在: $NFS_APP_DIR"
    exit 1
fi

# 设置目标路径（最小化本地存储使用）
TARGET_DIR="/home/<USER>/s5p6818-app"
mkdir -p $TARGET_DIR

echo "=== 创建符号链接方案（节省存储空间） ==="

# 只复制必要的可执行文件到本地
echo "复制应用程序到本地..."
cp $NFS_APP_DIR/bin/s5p6818-qt-glibc218-compatible-arm $TARGET_DIR/
chmod +x $TARGET_DIR/s5p6818-qt-glibc218-compatible-arm

# 检查复制是否成功
if [ ! -f $TARGET_DIR/s5p6818-qt-glibc218-compatible-arm ]; then
    echo "错误: 应用程序复制失败，可能是存储空间不足"
    echo "尝试直接从NFS运行..."
    TARGET_DIR="$NFS_APP_DIR"
fi

# 创建启动脚本（使用NFS上的FFmpeg库）
echo "创建启动脚本..."
cat > $TARGET_DIR/start-nfs.sh << 'STARTEOF'
#!/bin/sh
echo "=== 从NFS启动s5p6818 Qt RTSP播放器 ==="

# 设置库路径（直接使用NFS上的FFmpeg库）
export LD_LIBRARY_PATH=/nfs/s5p6818-app/ffmpeg-lib:/usr/lib/arm-linux-gnueabihf:$LD_LIBRARY_PATH

# 设置Qt显示环境
export QT_QPA_PLATFORM=linuxfb
export QT_QPA_FB_DRM=1

# 检查显示设备
if [ -e /dev/fb0 ]; then
    echo "找到帧缓冲设备: /dev/fb0"
else
    echo "警告: 未找到帧缓冲设备"
fi

# 检查FFmpeg库
echo "检查FFmpeg库路径..."
ls /nfs/s5p6818-app/ffmpeg-lib/*.so.* | head -3

# 选择运行方式
if [ -f /home/<USER>/s5p6818-app/s5p6818-qt-glibc218-compatible-arm ]; then
    echo "从本地运行应用程序..."
    cd /home/<USER>/s5p6818-app
    ./s5p6818-qt-glibc218-compatible-arm
else
    echo "从NFS直接运行应用程序..."
    cd /nfs/s5p6818-app/bin
    ./s5p6818-qt-glibc218-compatible-arm
fi
STARTEOF

chmod +x $TARGET_DIR/start-nfs.sh

# 创建环境测试脚本
echo "创建环境测试脚本..."
cat > $TARGET_DIR/test-nfs-env.sh << 'TESTEOF'
#!/bin/sh
echo "=== s5p6818 NFS环境测试 ==="

echo "1. 磁盘空间检查:"
if command -v df >/dev/null 2>&1; then
    df -h
else
    busybox df 2>/dev/null || echo "df命令不可用"
fi

echo "2. 内存信息:"
free 2>/dev/null || cat /proc/meminfo | head -3

echo "3. NFS挂载检查:"
mount | grep nfs || echo "未找到NFS挂载"

echo "4. 应用程序检查:"
if [ -f /home/<USER>/s5p6818-app/s5p6818-qt-glibc218-compatible-arm ]; then
    echo "本地应用程序: 存在"
    ls -lh /home/<USER>/s5p6818-app/s5p6818-qt-glibc218-compatible-arm
else
    echo "本地应用程序: 不存在，将从NFS运行"
fi

if [ -f /nfs/s5p6818-app/bin/s5p6818-qt-glibc218-compatible-arm ]; then
    echo "NFS应用程序: 存在"
    ls -lh /nfs/s5p6818-app/bin/s5p6818-qt-glibc218-compatible-arm
fi

echo "5. FFmpeg库检查:"
if [ -d /nfs/s5p6818-app/ffmpeg-lib ]; then
    echo "NFS FFmpeg库: 存在"
    ls /nfs/s5p6818-app/ffmpeg-lib/*.so.* | wc -l
    echo "库文件总大小:"
    du -sh /nfs/s5p6818-app/ffmpeg-lib 2>/dev/null || echo "无法计算大小"
else
    echo "NFS FFmpeg库: 不存在"
fi

echo "6. 库依赖检查:"
if command -v ldd >/dev/null 2>&1; then
    if [ -f /home/<USER>/s5p6818-app/s5p6818-qt-glibc218-compatible-arm ]; then
        ldd /home/<USER>/s5p6818-app/s5p6818-qt-glibc218-compatible-arm | head -5
    else
        ldd /nfs/s5p6818-app/bin/s5p6818-qt-glibc218-compatible-arm | head -5
    fi
else
    echo "ldd命令不可用"
fi

echo "7. 显示设备:"
ls -la /dev/fb* 2>/dev/null || echo "未找到帧缓冲设备"

echo "8. 推荐启动方式:"
if [ -f /home/<USER>/s5p6818-app/start-nfs.sh ]; then
    echo "/home/<USER>/s5p6818-app/start-nfs.sh"
elif [ -f /nfs/s5p6818-app/start-nfs.sh ]; then
    echo "/nfs/s5p6818-app/start-nfs.sh"
else
    echo "启动脚本未找到"
fi
TESTEOF

chmod +x $TARGET_DIR/test-nfs-env.sh

echo "=== 部署完成 ==="
echo "环境测试: $TARGET_DIR/test-nfs-env.sh"
echo "启动命令: $TARGET_DIR/start-nfs.sh"
echo ""
echo "注意: 此方案直接使用NFS上的FFmpeg库，节省本地存储空间"
