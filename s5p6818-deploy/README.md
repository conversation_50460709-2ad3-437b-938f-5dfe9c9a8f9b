# s5p6818开发板Qt RTSP视频流播放器部署包

## 版本信息
- Qt版本：5.15.3 (ARM交叉编译)
- FFmpeg版本：5.0.1 (ARM交叉编译)
- 目标平台：s5p6818开发板 (ARM Cortex-A53)
- 显示：800x480触摸屏

## 部署说明

### 1. 传输到开发板
```bash
# 通过NFS或SCP传输整个s5p6818-deploy目录到开发板
scp -r s5p6818-deploy/ root@<开发板IP>:/tmp/
```

### 2. 在开发板上执行部署
```bash
cd /tmp/s5p6818-deploy
sudo ./deploy.sh
```

### 3. 启动应用程序
```bash
/home/<USER>/s5p6818-app/start.sh
```

## 功能特性
- RTSP视频流播放
- 实时拍照功能
- 视频录制功能
- 触摸屏优化界面
- FFmpeg 5.0.1硬件加速支持

## 库依赖
- Qt5: libQt5Core.so.5, libQt5Gui.so.5, libQt5Widgets.so.5
- FFmpeg: libavformat.so.59, libavcodec.so.59, libavutil.so.57, libswscale.so.6
- 系统库: libc.so.6, libstdc++.so.6, libgcc_s.so.1

## 故障排除
如果遇到库加载问题，请检查：
1. LD_LIBRARY_PATH环境变量设置
2. FFmpeg库是否正确安装到/usr/local/lib/ffmpeg
3. Qt库是否存在于/usr/lib/arm-linux-gnueabihf
