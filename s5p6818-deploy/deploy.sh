#!/bin/bash
echo "=== s5p6818开发板部署脚本 ==="

# 设置目标路径
TARGET_DIR="/home/<USER>/s5p6818-app"
FFMPEG_LIB_DIR="/usr/local/lib/ffmpeg"

echo "创建目标目录..."
mkdir -p $TARGET_DIR
mkdir -p $FFMPEG_LIB_DIR

echo "复制应用程序..."
cp bin/s5p6818-qt-glibc218-compatible-arm $TARGET_DIR/
chmod +x $TARGET_DIR/s5p6818-qt-glibc218-compatible-arm

echo "复制FFmpeg库..."
cp ffmpeg-lib/* $FFMPEG_LIB_DIR/

echo "更新库缓存..."
ldconfig

echo "创建启动脚本..."
cat > $TARGET_DIR/start.sh << 'STARTEOF'
#!/bin/bash
export LD_LIBRARY_PATH=/usr/local/lib/ffmpeg:/usr/lib/arm-linux-gnueabihf:$LD_LIBRARY_PATH
export QT_QPA_PLATFORM=linuxfb
export QT_QPA_FB_DRM=1
cd /home/<USER>/s5p6818-app
./s5p6818-qt-glibc218-compatible-arm
STARTEOF

chmod +x $TARGET_DIR/start.sh

echo "部署完成！"
echo "启动命令：$TARGET_DIR/start.sh"
