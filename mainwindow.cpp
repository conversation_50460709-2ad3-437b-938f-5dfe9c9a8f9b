#include "mainwindow.h"
#include "ui_mainwindow.h"
#include <QFileInfo>
#include <QRegExp>
#include <QThread>
#include <QFile>



MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
    , m_frameTimer(new QTimer(this))
    , m_isConnected(false)
    , m_ffmpegProcess(nullptr)
    , m_currentRtspUrl("")
    , m_captureImagePath("")
    , m_recordVideoPath("")
    , m_isRecording(false)
    , m_formatContext(nullptr)
    , m_codecContext(nullptr)
    , m_codec(nullptr)
    , m_frame(nullptr)
    , m_frameRGB(nullptr)
    , m_packet(nullptr)
    , m_swsContext(nullptr)
    , m_buffer(nullptr)
    , m_videoStreamIndex(-1)
    , m_recordFormatContext(nullptr)
    , m_recordCodecContext(nullptr)
    , m_recordVideoStream(nullptr)
    , m_recordCodec(nullptr)
    , m_recordingInitialized(false)
    , m_recordFrameCount(0)
{
    ui->setupUi(this);

    // s5p6818专用显示配置
    this->resize(800, 480);  // 适配800x480触摸屏
    this->move(0, 0);        // 全屏显示

    // 启用触摸支持
    setAttribute(Qt::WA_AcceptTouchEvents);

    qDebug() << "s5p6818 CameraStream 初始化 - 窗口尺寸: 800x480";
    qDebug() << "触摸屏支持已启用";

    setupUI();
    setupTouchOptimization();  // 新增触摸优化
    setupSerialDebugSupport(); // 新增串口调试支持

    // 连接定时器信号 - 使用默认连接方式确保实时性
    connect(m_frameTimer, &QTimer::timeout, this, &MainWindow::updateVideoFrame);

    qDebug() << "s5p6818 RTSP视频流播放器初始化完成";
}

MainWindow::~MainWindow()
{
    qDebug() << "MainWindow析构函数开始";

    // 停止定时器
    if (m_frameTimer) {
        m_frameTimer->stop();
    }

    // 停止RTSP流并清理资源
    stopRTSPStream();

    // 清理FFmpeg资源
    cleanupFFmpegResources();

    delete ui;
    qDebug() << "MainWindow析构函数完成";
}

void MainWindow::setupUI()
{
    // 设置窗口标题
    setWindowTitle("RTSP视频流播放器");

    // 设置初始状态
    updateConnectionState(false);

    // 设置默认RTSP地址 - 使用摄像头实际IP地址
    if (ui->rtspUrlEdit->text().isEmpty()) {
        // 使用摄像头当前实际IP地址 *************
        ui->rtspUrlEdit->setText("rtsp://admin:admin2004@*************:554/Streaming/Channels/1");

        // 在状态标签中显示网络配置信息
        ui->statusLabel->setText("状态：未连接 | 摄像头IP: ************* (直连地址)");
    }

    // 设置状态标签
    ui->statusLabel->setText("状态：未连接");

    // 设置视频显示区域
    ui->videoLabel->setScaledContents(true);
    ui->videoLabel->setAlignment(Qt::AlignCenter);

    // 初始化拍照和录像功能
    updateRecordingState(false);

    // 创建保存目录
    QString picturesPath = QStandardPaths::writableLocation(QStandardPaths::PicturesLocation);
    QString videosPath = QStandardPaths::writableLocation(QStandardPaths::MoviesLocation);

    QDir().mkpath(picturesPath + "/CameraCaptures");
    QDir().mkpath(videosPath + "/CameraRecordings");

    qDebug() << "拍照保存路径:" << picturesPath + "/CameraCaptures";
    qDebug() << "录像保存路径:" << videosPath + "/CameraRecordings";

    // 网络配置已简化，用户需要手动配置网络
}

// s5p6818触摸屏优化设置
void MainWindow::setupTouchOptimization()
{
    qDebug() << "设置s5p6818触摸屏优化...";

    // 设置最小按钮尺寸以适配触摸操作
    const int minButtonSize = 60;
    const int buttonSpacing = 10;

    // 优化按钮尺寸
    if (ui->connectButton) {
        ui->connectButton->setMinimumSize(minButtonSize, minButtonSize);
        ui->connectButton->setStyleSheet("QPushButton { font-size: 14px; padding: 8px; }");
    }

    if (ui->disconnectButton) {
        ui->disconnectButton->setMinimumSize(minButtonSize, minButtonSize);
        ui->disconnectButton->setStyleSheet("QPushButton { font-size: 14px; padding: 8px; }");
    }

    if (ui->captureButton) {
        ui->captureButton->setMinimumSize(minButtonSize, minButtonSize);
        ui->captureButton->setStyleSheet("QPushButton { font-size: 14px; padding: 8px; }");
    }

    if (ui->recordButton) {
        ui->recordButton->setMinimumSize(minButtonSize, minButtonSize);
        ui->recordButton->setStyleSheet("QPushButton { font-size: 14px; padding: 8px; }");
    }

    // 优化RTSP输入框
    if (ui->rtspUrlEdit) {
        ui->rtspUrlEdit->setMinimumHeight(40);
        ui->rtspUrlEdit->setStyleSheet("QLineEdit { font-size: 12px; padding: 5px; }");
    }

    // 优化状态标签
    if (ui->statusLabel) {
        ui->statusLabel->setStyleSheet("QLabel { font-size: 12px; padding: 5px; }");
    }

    qDebug() << "触摸屏优化设置完成";
}

// 串口调试支持设置
void MainWindow::setupSerialDebugSupport()
{
    qDebug() << "设置串口调试支持...";

    // 启用键盘焦点
    setFocusPolicy(Qt::StrongFocus);

    // 设置快捷键
    QShortcut *connectShortcut = new QShortcut(QKeySequence("Ctrl+C"), this);
    connect(connectShortcut, &QShortcut::activated, this, &MainWindow::on_connectButton_clicked);

    QShortcut *disconnectShortcut = new QShortcut(QKeySequence("Ctrl+D"), this);
    connect(disconnectShortcut, &QShortcut::activated, this, &MainWindow::on_disconnectButton_clicked);

    QShortcut *captureShortcut = new QShortcut(QKeySequence("Ctrl+P"), this);
    connect(captureShortcut, &QShortcut::activated, this, &MainWindow::on_captureButton_clicked);

    QShortcut *recordShortcut = new QShortcut(QKeySequence("Ctrl+R"), this);
    connect(recordShortcut, &QShortcut::activated, this, &MainWindow::on_recordButton_clicked);

    qDebug() << "串口调试支持设置完成";
    qDebug() << "快捷键: Ctrl+C(连接), Ctrl+D(断开), Ctrl+P(拍照), Ctrl+R(录像)";
}

// 事件处理 - 支持触摸和键盘输入
bool MainWindow::event(QEvent *event)
{
    switch (event->type()) {
        case QEvent::TouchBegin:
        case QEvent::TouchUpdate:
        case QEvent::TouchEnd:
            handleTouchEvent(static_cast<QTouchEvent*>(event));
            return true;

        case QEvent::KeyPress:
            handleKeyboardInput(static_cast<QKeyEvent*>(event));
            return true;

        default:
            return QMainWindow::event(event);
    }
}

// 触摸事件处理
void MainWindow::handleTouchEvent(QTouchEvent *touchEvent)
{
    qDebug() << "触摸事件:" << touchEvent->type();

    if (touchEvent->touchPoints().size() > 0) {
        QTouchEvent::TouchPoint touchPoint = touchEvent->touchPoints().first();
        QPointF pos = touchPoint.pos();

        qDebug() << "触摸位置:" << pos;

        // 可以在这里添加特殊的触摸处理逻辑
        // 例如：长按、多点触控等
    }

    touchEvent->accept();
}

// 键盘输入处理 - 支持串口调试
void MainWindow::handleKeyboardInput(QKeyEvent *keyEvent)
{
    qDebug() << "键盘输入:" << keyEvent->key() << "文本:" << keyEvent->text();

    // 数字键快速选择功能
    switch (keyEvent->key()) {
        case Qt::Key_1:
            qDebug() << "快捷键: 连接RTSP";
            on_connectButton_clicked();
            break;

        case Qt::Key_2:
            qDebug() << "快捷键: 断开连接";
            on_disconnectButton_clicked();
            break;

        case Qt::Key_3:
            qDebug() << "快捷键: 拍照";
            on_captureButton_clicked();
            break;

        case Qt::Key_4:
            qDebug() << "快捷键: 录像";
            on_recordButton_clicked();
            break;

        case Qt::Key_Q:
            qDebug() << "快捷键: 退出程序";
            close();
            break;

        case Qt::Key_H:
            qDebug() << "=== 快捷键帮助 ===";
            qDebug() << "1: 连接RTSP";
            qDebug() << "2: 断开连接";
            qDebug() << "3: 拍照";
            qDebug() << "4: 录像";
            qDebug() << "Q: 退出程序";
            qDebug() << "H: 显示帮助";
            break;

        default:
            // 传递给父类处理
            QMainWindow::keyPressEvent(keyEvent);
            break;
    }
}

void MainWindow::on_connectButton_clicked()
{
    QString rtspUrl = ui->rtspUrlEdit->text().trimmed();

    if (rtspUrl.isEmpty()) {
        QMessageBox::warning(this, "警告", "请输入RTSP地址");
        return;
    }

    if (m_isConnected) {
        QMessageBox::information(this, "提示", "已经连接中，请先断开连接");
        return;
    }

    // 检查FFmpeg是否可用
    if (!isFFmpegAvailable()) {
        QMessageBox::critical(this, "错误",
            "FFmpeg不可用！\n"
            "请确保已正确安装FFmpeg并配置环境变量。\n"
            "Ubuntu安装命令：sudo apt install ffmpeg");
        return;
    }

    // 执行网络连通性检查
    QString host = extractHostFromRtspUrl(rtspUrl);
    ui->statusLabel->setText("状态：正在检查网络连通性...");

    if (!checkNetworkConnectivity(host)) {
        ui->statusLabel->setText("状态：网络连通性检查失败");
        performNetworkDiagnostics(host);
        return;
    }

    m_currentRtspUrl = rtspUrl;
    startRTSPStream(rtspUrl);
}

void MainWindow::on_disconnectButton_clicked()
{
    stopRTSPStream();
}



void MainWindow::startRTSPStream(const QString &rtspUrl)
{
    qDebug() << "开始连接RTSP流:" << rtspUrl;
    ui->statusLabel->setText("状态：正在连接RTSP流...");

    // 只清理资源，不更新UI状态
    if (m_frameTimer && m_frameTimer->isActive()) {
        m_frameTimer->stop();
    }
    cleanupFFmpegResources();
    qDebug() << "已清理之前的资源，开始初始化FFmpeg";

    // 初始化FFmpeg
    bool initResult = initializeFFmpeg(rtspUrl);
    qDebug() << "initializeFFmpeg返回结果:" << initResult;

    if (initResult) {
        m_isConnected = true;
        qDebug() << "设置m_isConnected = true，调用updateConnectionState(true)";
        updateConnectionState(true);

        // 启动定时器更新视频帧 - 优化为更高频率
        m_frameTimer->start(33); // 每33ms读取新帧，约30FPS

        ui->statusLabel->setText("状态：RTSP连接成功，正在接收视频流...");
        qDebug() << "FFmpeg初始化成功，RTSP地址:" << rtspUrl;

        // 验证状态设置
        qDebug() << "连接成功后验证 - m_isConnected:" << m_isConnected;
        qDebug() << "连接按钮启用状态:" << ui->connectButton->isEnabled();
        qDebug() << "断开按钮启用状态:" << ui->disconnectButton->isEnabled();
    } else {
        ui->statusLabel->setText("状态：RTSP连接失败");
        QMessageBox::critical(this, "错误",
                             "无法连接到RTSP流！\n\n"
                             "请检查：\n"
                             "1. RTSP地址是否正确\n"
                             "2. 网络连接是否正常\n"
                             "3. 摄像头是否在线");
        cleanupFFmpegResources();
    }
}

void MainWindow::stopRTSPStream()
{
    qDebug() << "停止RTSP流开始";

    // 停止定时器
    if (m_frameTimer && m_frameTimer->isActive()) {
        m_frameTimer->stop();
    }

    // 清理FFmpeg资源
    cleanupFFmpegResources();

    m_isConnected = false;

    // 清空视频显示区域
    if (ui && ui->videoLabel) {
        ui->videoLabel->clear();
        ui->videoLabel->setText("视频显示区域");
    }

    // 更新UI状态
    if (ui) {
        updateConnectionState(false);
        if (ui->statusLabel) {
            ui->statusLabel->setText("状态：已断开连接");
        }
    }

    qDebug() << "停止RTSP流完成";
}

void MainWindow::cleanupFFmpeg()
{
    if (m_ffmpegProcess) {
        qDebug() << "开始清理FFmpeg进程，当前状态:" << m_ffmpegProcess->state();

        // 断开所有信号连接，防止在清理过程中触发槽函数
        m_ffmpegProcess->disconnect();

        if (m_ffmpegProcess->state() != QProcess::NotRunning) {
            qDebug() << "正在终止FFmpeg进程...";

            // 首先尝试优雅地终止进程
            m_ffmpegProcess->terminate();
            if (!m_ffmpegProcess->waitForFinished(2000)) {
                // 如果优雅终止失败，强制杀死进程
                qDebug() << "优雅终止失败，强制杀死FFmpeg进程";
                m_ffmpegProcess->kill();
                m_ffmpegProcess->waitForFinished(1000);
            }
        }

        // 直接删除，避免延迟删除可能导致的问题
        m_ffmpegProcess->deleteLater();
        m_ffmpegProcess = nullptr;

        qDebug() << "FFmpeg进程清理完成";
    }
}

bool MainWindow::isFFmpegAvailable()
{
    QProcess testProcess;
    testProcess.start("ffmpeg", QStringList() << "-version");
    bool result = testProcess.waitForFinished(3000) && testProcess.exitCode() == 0;
    qDebug() << "FFmpeg可用性检查:" << result;
    return result;
}

void MainWindow::updateVideoFrame()
{
    // 基本检查：确保连接状态和UI有效
    if (!m_isConnected || !ui || !ui->videoLabel) {
        return;
    }

    // 在锁外声明变量
    QPixmap pixmap;
    bool frameReady = false;
    bool shouldRecord = false;

    // 最小化锁的持有时间 - 只在读取帧数据时持有锁
    {
        QMutexLocker locker(&m_frameMutex);

        // 读取下一帧
        if (readNextFrame()) {
            // 将AVFrame转换为QPixmap
            pixmap = convertFrameToPixmap(m_frameRGB);
            frameReady = !pixmap.isNull();
            shouldRecord = m_isRecording && m_recordingInitialized;
        }
    } // 锁在这里释放

    // 在锁外进行UI更新和其他耗时操作
    if (frameReady) {
        // 缩放图片以适应显示区域
        QPixmap scaledPixmap = pixmap.scaled(ui->videoLabel->size(),
                                           Qt::KeepAspectRatio,
                                           Qt::SmoothTransformation);
        ui->videoLabel->setPixmap(scaledPixmap);

        // 更新状态显示帧数信息
        static int frameCount = 0;
        frameCount++;
        if (frameCount % 25 == 0 && ui->statusLabel) { // 每25帧更新一次状态
            ui->statusLabel->setText(QString("状态：已连接 - 正在播放 (已接收 %1 帧)").arg(frameCount));
        }

        // 如果正在录像，写入帧到录像文件
        if (shouldRecord) {
            // 优化帧率控制：目标10FPS，显示30FPS，所以每3帧写入1帧
            static int frameSkipCounter = 0;
            static auto lastRecordTime = std::chrono::steady_clock::now();

            frameSkipCounter++;

            // 时间控制：确保录像帧率稳定在10FPS
            auto currentTime = std::chrono::steady_clock::now();
            auto timeSinceLastRecord = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - lastRecordTime);

            // 每100ms写入一帧（10FPS），或者每3帧强制写入一次
            bool shouldWriteFrame = (frameSkipCounter >= 3) || (timeSinceLastRecord.count() >= 100);

            if (shouldWriteFrame) {
                frameSkipCounter = 0;
                lastRecordTime = currentTime;

                // 调试：检查帧数据（减少输出频率）
                static int debugCounter = 0;
                debugCounter++;
                if (debugCounter % 5 == 1 && m_frameRGB) {
                    qDebug() << "录像帧" << debugCounter << ": " << m_frameRGB->width << "x" << m_frameRGB->height
                             << "间隔" << timeSinceLastRecord.count() << "ms";
                }

                // 使用FFmpeg C库写入帧数据
                if (!writeVideoFrame(m_frameRGB)) {
                    qDebug() << "写入录像帧失败";
                } else {
                    static int successCount = 0;
                    successCount++;
                    if (successCount % 5 == 1) { // 每5帧打印一次成功信息
                        qDebug() << "录像帧写入成功，已写入" << successCount << "帧";
                    }
                }
            }
        }
    }
}

void MainWindow::onFFmpegFinished(int exitCode, QProcess::ExitStatus exitStatus)
{
    qDebug() << "FFmpeg进程结束，退出码:" << exitCode << "状态:" << exitStatus;

    // 安全检查：确保UI对象仍然有效
    if (!ui || !ui->statusLabel) {
        qDebug() << "onFFmpegFinished: UI对象无效";
        return;
    }

    if (m_isConnected) {
        if (exitCode != 0) {
            // 读取错误输出以诊断问题
            QString errorOutput;
            if (m_ffmpegProcess) {
                errorOutput = m_ffmpegProcess->readAllStandardError();
                qDebug() << "FFmpeg错误输出:" << errorOutput;
            }

            // 检查是否是参数错误
            if (errorOutput.contains("Unrecognized option") ||
                errorOutput.contains("Option not found") ||
                errorOutput.contains("updatefirst")) {

                if (ui->statusLabel) {
                    ui->statusLabel->setText("状态：FFmpeg参数错误，已修复");
                }
                QMessageBox::information(this, "参数已修复",
                    "检测到FFmpeg参数不兼容问题。\n"
                    "已自动修复，请重新尝试连接。\n\n"
                    "修复内容：移除了不兼容的'-updatefirst'参数");
            } else {
                if (ui->statusLabel) {
                    ui->statusLabel->setText("状态：RTSP连接失败");
                }
                QMessageBox::warning(this, "连接失败",
                    "RTSP流连接失败！\n\n"
                    "请检查：\n"
                    "1. RTSP地址是否正确\n"
                    "2. 用户名密码是否正确\n"
                    "3. 摄像头是否在线\n"
                    "4. 网络连接是否正常\n\n"
                    "错误详情：" + errorOutput.left(100));
            }
        }
        stopRTSPStream();
    }
}

void MainWindow::onFFmpegError(QProcess::ProcessError error)
{
    qDebug() << "FFmpeg进程错误:" << error;

    // 安全检查：确保UI对象仍然有效
    if (!ui || !ui->statusLabel) {
        qDebug() << "onFFmpegError: UI对象无效";
        return;
    }

    ui->statusLabel->setText("状态：FFmpeg进程错误");

    QString errorMsg;
    switch (error) {
        case QProcess::FailedToStart:
            errorMsg = "FFmpeg启动失败！请确保已正确安装FFmpeg。\n\n安装命令：sudo apt install ffmpeg";
            break;
        case QProcess::Crashed:
            errorMsg = "FFmpeg进程崩溃！可能是RTSP流格式不兼容。";
            break;
        case QProcess::Timedout:
            errorMsg = "FFmpeg进程超时！网络连接可能不稳定。";
            break;
        default:
            errorMsg = "FFmpeg进程发生未知错误！";
            break;
    }

    QMessageBox::critical(this, "FFmpeg错误", errorMsg);
    stopRTSPStream();
}

void MainWindow::onFFmpegErrorOutput()
{
    if (m_ffmpegProcess) {
        QByteArray data = m_ffmpegProcess->readAllStandardError();
        QString errorOutput = QString::fromUtf8(data);
        qDebug() << "FFmpeg错误输出:" << errorOutput;

        // 检查特定的错误模式并提供针对性建议
        if (errorOutput.contains("401 Unauthorized") || errorOutput.contains("Authentication failed")) {
            ui->statusLabel->setText("状态：认证失败 - 请检查用户名密码");
            QMessageBox::warning(this, "认证失败",
                               "RTSP认证失败！\n\n建议尝试：\n"
                               "1. 检查用户名密码是否正确\n"
                               "2. 尝试无认证URL：rtsp://*************:554/Streaming/Channels/1\n"
                               "3. 通过Web界面(http://*************)检查RTSP设置");
        } else if (errorOutput.contains("Connection refused") || errorOutput.contains("Connection timed out")) {
            ui->statusLabel->setText("状态：连接被拒绝 - 请检查RTSP服务");
            QMessageBox::warning(this, "连接失败",
                               "无法连接到RTSP服务！\n\n建议检查：\n"
                               "1. 摄像头RTSP服务是否启用\n"
                               "2. 端口554是否正确\n"
                               "3. 防火墙设置");
        } else if (errorOutput.contains("No route to host")) {
            ui->statusLabel->setText("状态：网络不可达");
        } else if (errorOutput.contains("Invalid data found") || errorOutput.contains("Protocol not found")) {
            ui->statusLabel->setText("状态：RTSP协议错误 - 请尝试其他URL格式");
            QMessageBox::information(this, "协议错误",
                                   "RTSP协议错误！\n\n建议尝试以下URL格式：\n"
                                   "• rtsp://admin:admin2004@*************:554/Streaming/Channels/101\n"
                                   "• rtsp://admin:admin2004@*************:554/h264/ch1/main/av_stream\n"
                                   "• rtsp://admin:admin2004@*************:554/cam/realmonitor?channel=1&subtype=0");
        }
    }
}

void MainWindow::onFFmpegStandardOutput()
{
    if (m_ffmpegProcess) {
        QByteArray data = m_ffmpegProcess->readAllStandardOutput();
        QString output = QString::fromUtf8(data);
        qDebug() << "FFmpeg标准输出:" << output;

        // 检查成功连接的标志
        if (output.contains("Stream #0") || output.contains("Video:")) {
            ui->statusLabel->setText("状态：检测到视频流信息");
        }
    }
}

void MainWindow::updateConnectionState(bool connected)
{
    qDebug() << "更新连接状态:" << (connected ? "已连接" : "未连接");

    ui->connectButton->setEnabled(!connected);
    ui->disconnectButton->setEnabled(connected);
    ui->rtspUrlEdit->setEnabled(!connected);

    // 拍照和录像按钮只在连接时可用
    if (ui->captureButton) {
        ui->captureButton->setEnabled(connected && !m_isRecording);
    }
    if (ui->recordButton) {
        ui->recordButton->setEnabled(connected);
    }

    // 如果断开连接时正在录像，停止录像
    if (!connected && m_isRecording) {
        stopRecording();
    }
}

QString MainWindow::extractHostFromRtspUrl(const QString &rtspUrl)
{
    // 解析RTSP URL获取主机名
    // rtsp://admin:<EMAIL>:554/Streaming/Channels/1
    QRegExp rx("rtsp://(?:[^@]+@)?([^:/]+)");
    if (rx.indexIn(rtspUrl) != -1) {
        return rx.cap(1);
    }
    return "*************"; // 摄像头实际IP地址
}

bool MainWindow::checkNetworkConnectivity(const QString &host)
{
    qDebug() << "检查网络连通性到主机:" << host;

    // 使用ping命令检查连通性
    QProcess pingProcess;
    QStringList arguments;
    arguments << "-c" << "3" << "-W" << "3" << host;

    pingProcess.start("ping", arguments);
    bool finished = pingProcess.waitForFinished(10000); // 10秒超时

    if (!finished) {
        qDebug() << "Ping超时";
        return false;
    }

    int exitCode = pingProcess.exitCode();
    QString output = pingProcess.readAllStandardOutput();
    QString errorOutput = pingProcess.readAllStandardError();

    qDebug() << "Ping退出码:" << exitCode;
    qDebug() << "Ping输出:" << output;
    qDebug() << "Ping错误:" << errorOutput;

    return exitCode == 0;
}

void MainWindow::performNetworkDiagnostics(const QString &host)
{
    qDebug() << "网络连通性检查失败，目标主机:" << host;

    // 简化的网络诊断提示
    QString diagnosticInfo = "网络连接失败诊断：\n\n";
    diagnosticInfo += "无法连接到摄像头主机: " + host + "\n\n";
    diagnosticInfo += "=== 建议解决方案 ===\n";
    diagnosticInfo += "1. 检查网络连接：\n";
    diagnosticInfo += "   - 确认摄像头电源已开启\n";
    diagnosticInfo += "   - 检查网线连接是否正常\n";
    diagnosticInfo += "   - 验证摄像头IP地址是否正确\n\n";
    diagnosticInfo += "2. 网络配置检查：\n";
    diagnosticInfo += "   - 确认计算机和摄像头在同一网络段\n";
    diagnosticInfo += "   - 检查防火墙设置是否阻止了RTSP端口(554)\n";
    diagnosticInfo += "   - 尝试在终端运行: ping " + host + "\n\n";
    diagnosticInfo += "3. 手动网络配置：\n";
    diagnosticInfo += "   - 如果使用APIPA地址(169.254.x.x)，请手动配置网络接口\n";
    diagnosticInfo += "   - 设置静态IP地址与摄像头在同一网段\n";
    diagnosticInfo += "   - 例如：sudo ip addr add **************/16 dev ens37\n\n";
    diagnosticInfo += "4. RTSP连接测试：\n";
    diagnosticInfo += "   - 测试端口连通性: telnet " + host + " 554\n";
    diagnosticInfo += "   - 验证RTSP URL格式是否正确\n";
    diagnosticInfo += "   - 检查用户名和密码是否正确\n";

    // 显示简化的诊断信息
    QMessageBox msgBox;
    msgBox.setWindowTitle("网络连接失败");
    msgBox.setText("无法连接到摄像头，请检查网络配置");
    msgBox.setDetailedText(diagnosticInfo);
    msgBox.setIcon(QMessageBox::Warning);
    msgBox.exec();
}

// ==================== 拍照和录像功能实现 ====================

void MainWindow::on_captureButton_clicked()
{
    if (!m_isConnected) {
        QMessageBox::warning(this, "警告", "请先连接RTSP视频流");
        return;
    }

    ui->statusLabel->setText("状态：正在拍照...");
    captureCurrentFrame();
}

void MainWindow::on_recordButton_clicked()
{
    qDebug() << "录像按钮被点击";

    if (!m_isConnected) {
        qDebug() << "RTSP未连接，显示警告";
        QMessageBox::warning(this, "警告", "请先连接RTSP视频流");
        return;
    }

    if (m_isRecording) {
        qDebug() << "当前正在录像，执行停止录像";
        stopRecording();
    } else {
        qDebug() << "当前未录像，执行开始录像";
        startRecording();
    }
}

void MainWindow::captureCurrentFrame()
{
    if (!m_isConnected) {
        QMessageBox::warning(this, "警告", "请先连接RTSP视频流");
        return;
    }

    // 生成拍照文件名
    m_captureImagePath = generateCaptureFileName();

    // 使用非阻塞方式尝试获取帧数据
    QPixmap currentPixmap;
    bool usedFFmpegFrame = false;

    // 方法1：尝试获取FFmpeg原始帧数据（非阻塞）
    if (m_frameMutex.tryLock(5)) { // 最多等待5ms
        if (m_frameRGB && m_codecContext) {
            try {
                // 将当前帧转换为QPixmap
                currentPixmap = convertFrameToPixmap(m_frameRGB);
                usedFFmpegFrame = !currentPixmap.isNull();
                qDebug() << "使用FFmpeg原始帧数据拍照";
            } catch (...) {
                qDebug() << "FFmpeg帧转换异常，将使用UI图片";
            }
        }
        m_frameMutex.unlock();
    } else {
        qDebug() << "无法获取帧锁，将使用UI显示图片";
    }

    // 方法2：如果无法获取FFmpeg帧数据，则从UI控件获取当前显示的图片
    if (currentPixmap.isNull() && ui->videoLabel) {
        // 使用Qt推荐的新API获取pixmap（返回值而非指针）
        QPixmap labelPixmap = ui->videoLabel->pixmap(Qt::ReturnByValue);
        if (!labelPixmap.isNull()) {
            currentPixmap = labelPixmap;
            qDebug() << "使用UI显示的图片进行拍照";
        }
    }

    if (!currentPixmap.isNull()) {
        // 验证图片尺寸
        if (currentPixmap.width() > 0 && currentPixmap.height() > 0) {
            qDebug() << "准备保存图片，尺寸:" << currentPixmap.size();

            // 保存图片
            bool saveSuccess = false;
            try {
                saveSuccess = currentPixmap.save(m_captureImagePath, "PNG");
            } catch (...) {
                qDebug() << "保存图片时发生异常";
                saveSuccess = false;
            }

            if (saveSuccess) {
                ui->statusLabel->setText("状态：拍照成功 - " + QFileInfo(m_captureImagePath).fileName());

                // 获取文件信息
                QFileInfo fileInfo(m_captureImagePath);
                QString fileSize = QString::number(fileInfo.size() / 1024.0, 'f', 1) + " KB";
                QString dataSource = usedFFmpegFrame ? "FFmpeg原始帧" : "UI显示图片";

                // 使用QTimer延迟显示对话框，避免阻塞
                QTimer::singleShot(100, this, [this, fileSize, dataSource]() {
                    QMessageBox::information(this, "拍照成功",
                                           QString("图片已保存到：\n%1\n\n文件大小：%2\n数据源：%3")
                                           .arg(m_captureImagePath).arg(fileSize).arg(dataSource));
                });

                qDebug() << "拍照成功，保存到:" << m_captureImagePath << "数据源:" << dataSource;
            } else {
                ui->statusLabel->setText("状态：拍照失败 - 无法保存文件");
                QMessageBox::critical(this, "错误", "无法保存图片文件");
                qDebug() << "拍照失败，无法保存到:" << m_captureImagePath;
            }
        } else {
            ui->statusLabel->setText("状态：拍照失败 - 图片尺寸无效");
            QMessageBox::critical(this, "错误", "获取的图片尺寸无效");
            qDebug() << "拍照失败，图片尺寸无效:" << currentPixmap.size();
        }
    } else {
        ui->statusLabel->setText("状态：拍照失败 - 无可用图像数据");
        QMessageBox::critical(this, "错误", "当前无可用图像数据，无法拍照\n请确保视频流正在播放");
        qDebug() << "拍照失败，无可用图像数据";
    }
}

void MainWindow::startRecording()
{
    if (!m_isConnected) {
        QMessageBox::warning(this, "警告", "请先连接RTSP视频流");
        return;
    }

    // 生成录像文件名
    m_recordVideoPath = generateRecordFileName();
    qDebug() << "录像文件路径:" << m_recordVideoPath;

    // 使用FFmpeg C库进行录像
    if (initializeRecording(m_recordVideoPath)) {
        m_recordFrameCount = 0; // 重置帧计数器
        cleanupRecordingStaticResources(); // 触发静态资源重置
        m_isRecording = true;
        updateRecordingState(true);
        ui->statusLabel->setText("状态：正在录像... 文件:" + QFileInfo(m_recordVideoPath).fileName());
        qDebug() << "FFmpeg C库录像初始化成功";
    } else {
        ui->statusLabel->setText("状态：录像失败 - 初始化失败");
        QMessageBox::critical(this, "错误", "无法初始化FFmpeg录像功能");
        qDebug() << "FFmpeg C库录像初始化失败";
    }
}

void MainWindow::stopRecording()
{
    if (m_isRecording) {
        qDebug() << "停止FFmpeg C库录像";

        // 结束录像
        finalizeRecording();

        m_isRecording = false;
        updateRecordingState(false);
        ui->statusLabel->setText("状态：录像已停止");

        // 检查录像文件
        QFileInfo fileInfo(m_recordVideoPath);
        if (fileInfo.exists() && fileInfo.size() > 0) {
            QMessageBox::information(this, "录像完成",
                                   QString("录像已保存到：\n%1\n\n文件大小：%2 MB")
                                   .arg(m_recordVideoPath)
                                   .arg(fileInfo.size() / 1024.0 / 1024.0, 0, 'f', 1));
        } else {
            QMessageBox::warning(this, "录像警告", "录像文件可能未正确保存");
        }

        qDebug() << "录像已停止，文件保存到:" << m_recordVideoPath;
    } else {
        qDebug() << "停止录像：当前未在录像";
    }
}

void MainWindow::updateRecordingState(bool recording)
{
    m_isRecording = recording;

    if (ui->recordButton) {
        if (recording) {
            ui->recordButton->setText("停止录像");
            ui->recordButton->setStyleSheet("background-color: #ff4444; color: white;");
        } else {
            ui->recordButton->setText("开始录像");
            ui->recordButton->setStyleSheet("");
        }
    }

    // 录像时禁用拍照按钮，避免冲突
    if (ui->captureButton) {
        ui->captureButton->setEnabled(!recording);
    }
}

QString MainWindow::generateCaptureFileName()
{
    QString picturesPath = QStandardPaths::writableLocation(QStandardPaths::PicturesLocation);
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd_hh-mm-ss");
    return picturesPath + "/CameraCaptures/capture_" + timestamp + ".jpg";
}

QString MainWindow::generateRecordFileName()
{
    QString videosPath = QStandardPaths::writableLocation(QStandardPaths::MoviesLocation);
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd_hh-mm-ss");
    return videosPath + "/CameraRecordings/record_" + timestamp + ".mp4";
}

























// FFmpeg核心功能实现
bool MainWindow::initializeFFmpeg(const QString &rtspUrl)
{
    qDebug() << "初始化FFmpeg，RTSP地址:" << rtspUrl;

    // 清理之前的资源
    cleanupFFmpegResources();

    // 分配格式上下文
    m_formatContext = avformat_alloc_context();
    if (!m_formatContext) {
        qDebug() << "无法分配AVFormatContext";
        return false;
    }

    // 设置RTSP选项 - 优化低延迟
    AVDictionary *options = nullptr;
    av_dict_set(&options, "rtsp_transport", "tcp", 0);
    av_dict_set(&options, "stimeout", "5000000", 0); // 5秒超时
    av_dict_set(&options, "max_delay", "500000", 0); // 最大延迟500ms
    av_dict_set(&options, "reorder_queue_size", "1", 0); // 减少重排序队列
    av_dict_set(&options, "buffer_size", "1024000", 0); // 1MB缓冲区

    // 打开输入流
    if (avformat_open_input(&m_formatContext, rtspUrl.toUtf8().constData(), nullptr, &options) != 0) {
        qDebug() << "无法打开RTSP流:" << rtspUrl;
        av_dict_free(&options);
        return false;
    }
    av_dict_free(&options);

    // 查找流信息
    if (avformat_find_stream_info(m_formatContext, nullptr) < 0) {
        qDebug() << "无法找到流信息";
        return false;
    }

    // 查找视频流
    m_videoStreamIndex = -1;
    for (unsigned int i = 0; i < m_formatContext->nb_streams; i++) {
        if (m_formatContext->streams[i]->codecpar->codec_type == AVMEDIA_TYPE_VIDEO) {
            m_videoStreamIndex = i;
            break;
        }
    }

    if (m_videoStreamIndex == -1) {
        qDebug() << "未找到视频流";
        return false;
    }

    // 获取解码器参数
    AVCodecParameters *codecpar = m_formatContext->streams[m_videoStreamIndex]->codecpar;

    // 查找解码器
    m_codec = avcodec_find_decoder(codecpar->codec_id);
    if (!m_codec) {
        qDebug() << "未找到解码器";
        return false;
    }

    // 分配解码器上下文
    m_codecContext = avcodec_alloc_context3(m_codec);
    if (!m_codecContext) {
        qDebug() << "无法分配解码器上下文";
        return false;
    }

    // 复制解码器参数
    if (avcodec_parameters_to_context(m_codecContext, codecpar) < 0) {
        qDebug() << "无法复制解码器参数";
        return false;
    }

    // 设置解码器选项 - 优化低延迟
    AVDictionary *codec_options = nullptr;
    av_dict_set(&codec_options, "threads", "auto", 0); // 自动线程数
    av_dict_set(&codec_options, "thread_type", "frame", 0); // 帧级线程

    // 打开解码器
    if (avcodec_open2(m_codecContext, m_codec, &codec_options) < 0) {
        qDebug() << "无法打开解码器";
        av_dict_free(&codec_options);
        return false;
    }
    av_dict_free(&codec_options);

    // 分配帧
    m_frame = av_frame_alloc();
    m_frameRGB = av_frame_alloc();
    if (!m_frame || !m_frameRGB) {
        qDebug() << "无法分配帧";
        return false;
    }

    // 计算RGB帧所需的缓冲区大小
    int numBytes = av_image_get_buffer_size(AV_PIX_FMT_RGB24, m_codecContext->width, m_codecContext->height, 1);
    m_buffer = (uint8_t*)av_malloc(numBytes * sizeof(uint8_t));

    // 设置RGB帧的数据指针和尺寸信息
    av_image_fill_arrays(m_frameRGB->data, m_frameRGB->linesize, m_buffer, AV_PIX_FMT_RGB24,
                        m_codecContext->width, m_codecContext->height, 1);

    // 设置RGB帧的格式和尺寸信息（关键修复）
    m_frameRGB->format = AV_PIX_FMT_RGB24;
    m_frameRGB->width = m_codecContext->width;
    m_frameRGB->height = m_codecContext->height;

    // 初始化转换上下文
    m_swsContext = sws_getContext(m_codecContext->width, m_codecContext->height, m_codecContext->pix_fmt,
                                 m_codecContext->width, m_codecContext->height, AV_PIX_FMT_RGB24,
                                 SWS_BILINEAR, nullptr, nullptr, nullptr);

    if (!m_swsContext) {
        qDebug() << "无法初始化转换上下文";
        return false;
    }

    // 分配数据包
    m_packet = av_packet_alloc();
    if (!m_packet) {
        qDebug() << "无法分配数据包";
        return false;
    }

    qDebug() << "FFmpeg初始化成功";
    return true;
}

void MainWindow::cleanupFFmpegResources()
{
    qDebug() << "清理FFmpeg资源";

    // 清理录像资源
    finalizeRecording();

    // 清理转换上下文
    if (m_swsContext) {
        sws_freeContext(m_swsContext);
        m_swsContext = nullptr;
    }

    // 清理缓冲区
    if (m_buffer) {
        av_free(m_buffer);
        m_buffer = nullptr;
    }

    // 清理帧
    if (m_frame) {
        av_frame_free(&m_frame);
    }
    if (m_frameRGB) {
        av_frame_free(&m_frameRGB);
    }

    // 清理数据包
    if (m_packet) {
        av_packet_free(&m_packet);
    }

    // 清理解码器上下文
    if (m_codecContext) {
        avcodec_free_context(&m_codecContext);
    }

    // 清理格式上下文
    if (m_formatContext) {
        avformat_close_input(&m_formatContext);
    }

    m_videoStreamIndex = -1;
    qDebug() << "FFmpeg资源清理完成";
}

bool MainWindow::readNextFrame()
{
    if (!m_formatContext || !m_codecContext || !m_packet) {
        return false;
    }

    int frameCount = 0;
    const int maxFramesToRead = 5; // 最多读取5帧，跳过旧帧减少延迟

    // 读取数据包
    while (av_read_frame(m_formatContext, m_packet) >= 0 && frameCount < maxFramesToRead) {
        // 检查是否是视频流
        if (m_packet->stream_index == m_videoStreamIndex) {
            frameCount++;

            // 发送数据包到解码器
            int ret = avcodec_send_packet(m_codecContext, m_packet);
            if (ret < 0) {
                av_packet_unref(m_packet);
                continue;
            }

            // 接收解码后的帧
            ret = avcodec_receive_frame(m_codecContext, m_frame);
            if (ret == 0) {
                // 如果不是最新帧，继续读取下一帧
                if (frameCount < maxFramesToRead) {
                    av_packet_unref(m_packet);
                    continue;
                }

                // 转换像素格式
                sws_scale(m_swsContext, (uint8_t const * const *)m_frame->data,
                         m_frame->linesize, 0, m_codecContext->height,
                         m_frameRGB->data, m_frameRGB->linesize);

                av_packet_unref(m_packet);
                return true;
            }
        }
        av_packet_unref(m_packet);
    }

    return false;
}

QPixmap MainWindow::convertFrameToPixmap(AVFrame *frame)
{
    if (!frame || !m_codecContext) {
        return QPixmap();
    }

    // 创建QImage
    QImage image(frame->data[0], m_codecContext->width, m_codecContext->height,
                frame->linesize[0], QImage::Format_RGB888);

    // 转换为QPixmap
    return QPixmap::fromImage(image);
}

bool MainWindow::initializeRecording(const QString &filename)
{
    qDebug() << "初始化录像:" << filename;

    // 清理之前的录像资源
    finalizeRecording();

    // 分配输出格式上下文
    avformat_alloc_output_context2(&m_recordFormatContext, nullptr, "mp4", filename.toUtf8().constData());
    if (!m_recordFormatContext) {
        qDebug() << "无法分配输出格式上下文";
        return false;
    }

    // 查找H.264编码器
    m_recordCodec = avcodec_find_encoder(AV_CODEC_ID_H264);
    if (!m_recordCodec) {
        qDebug() << "未找到H.264编码器";
        return false;
    }

    // 创建视频流
    m_recordVideoStream = avformat_new_stream(m_recordFormatContext, nullptr);
    if (!m_recordVideoStream) {
        qDebug() << "无法创建视频流";
        return false;
    }

    // 分配编码器上下文
    m_recordCodecContext = avcodec_alloc_context3(m_recordCodec);
    if (!m_recordCodecContext) {
        qDebug() << "无法分配编码器上下文";
        return false;
    }

    // 设置编码参数 - 匹配实际录像帧率
    m_recordCodecContext->codec_id = AV_CODEC_ID_H264;
    m_recordCodecContext->bit_rate = 1500000; // 1.5Mbps，适合10FPS
    m_recordCodecContext->width = m_codecContext->width;
    m_recordCodecContext->height = m_codecContext->height;

    // 关键修复：设置实际录像帧率为10FPS（每3帧写入1帧）
    m_recordCodecContext->time_base = {1, 10}; // 10 FPS时间基
    m_recordCodecContext->framerate = {10, 1}; // 10 FPS帧率
    m_recordCodecContext->gop_size = 10; // GOP大小为1秒（10帧）
    m_recordCodecContext->max_b_frames = 1; // 减少B帧，适合低帧率
    m_recordCodecContext->pix_fmt = AV_PIX_FMT_YUV420P;

    // 设置H.264编码选项
    m_recordCodecContext->profile = FF_PROFILE_H264_MAIN;
    m_recordCodecContext->level = 31; // Level 3.1

    qDebug() << "录像编码器配置: 10FPS, 1.5Mbps, GOP=10";

    // 设置编码器选项
    if (m_recordFormatContext->oformat->flags & AVFMT_GLOBALHEADER) {
        m_recordCodecContext->flags |= AV_CODEC_FLAG_GLOBAL_HEADER;
    }

    // 设置编码器选项
    AVDictionary *codec_options = nullptr;
    av_dict_set(&codec_options, "preset", "medium", 0); // 平衡速度和质量
    av_dict_set(&codec_options, "crf", "23", 0); // 恒定质量模式
    av_dict_set(&codec_options, "tune", "zerolatency", 0); // 低延迟调优

    // 打开编码器
    if (avcodec_open2(m_recordCodecContext, m_recordCodec, &codec_options) < 0) {
        qDebug() << "无法打开编码器";
        av_dict_free(&codec_options);
        return false;
    }
    av_dict_free(&codec_options);

    // 复制编码器参数到流
    if (avcodec_parameters_from_context(m_recordVideoStream->codecpar, m_recordCodecContext) < 0) {
        qDebug() << "无法复制编码器参数";
        return false;
    }

    // 打开输出文件
    if (!(m_recordFormatContext->oformat->flags & AVFMT_NOFILE)) {
        if (avio_open(&m_recordFormatContext->pb, filename.toUtf8().constData(), AVIO_FLAG_WRITE) < 0) {
            qDebug() << "无法打开输出文件:" << filename;
            return false;
        }
    }

    // 写入文件头
    if (avformat_write_header(m_recordFormatContext, nullptr) < 0) {
        qDebug() << "无法写入文件头";
        return false;
    }

    m_recordingInitialized = true;
    qDebug() << "录像初始化成功";
    return true;
}

bool MainWindow::writeVideoFrame(AVFrame *frame)
{
    if (!m_recordingInitialized || !m_recordFormatContext || !m_recordCodecContext || !frame) {
        return false;
    }

    // 验证输入帧的有效性（关键修复）
    if (frame->width <= 0 || frame->height <= 0 || !frame->data[0]) {
        qDebug() << "输入帧数据无效: width=" << frame->width << "height=" << frame->height << "data=" << (void*)frame->data[0];
        return false;
    }

    // 分配YUV420P格式的帧
    static AVFrame *yuvFrame = nullptr;
    static struct SwsContext *recordSwsContext = nullptr;
    static int64_t frameIndex = 0;
    static bool needsReset = true; // 添加重置标志
    static auto startTime = std::chrono::steady_clock::now(); // 录像开始时间

    // 如果需要重置（新的录像会话）
    if (needsReset) {
        if (yuvFrame) {
            av_frame_free(&yuvFrame);
        }
        if (recordSwsContext) {
            sws_freeContext(recordSwsContext);
            recordSwsContext = nullptr;
        }
        frameIndex = 0;
        startTime = std::chrono::steady_clock::now(); // 重置开始时间
        needsReset = false;
        qDebug() << "重置录像静态资源和时间戳";
    }

    if (!yuvFrame) {
        yuvFrame = av_frame_alloc();
        if (!yuvFrame) {
            qDebug() << "无法分配YUV帧";
            return false;
        }

        yuvFrame->format = AV_PIX_FMT_YUV420P;
        yuvFrame->width = m_recordCodecContext->width;
        yuvFrame->height = m_recordCodecContext->height;

        if (av_frame_get_buffer(yuvFrame, 32) < 0) {
            qDebug() << "无法分配YUV帧缓冲区";
            av_frame_free(&yuvFrame);
            return false;
        }
    }

    // 创建RGB到YUV420P的转换上下文
    if (!recordSwsContext) {
        qDebug() << "创建录像转换上下文: 源尺寸" << frame->width << "x" << frame->height
                 << "目标尺寸" << m_recordCodecContext->width << "x" << m_recordCodecContext->height;

        recordSwsContext = sws_getContext(
            frame->width, frame->height, AV_PIX_FMT_RGB24,
            m_recordCodecContext->width, m_recordCodecContext->height, AV_PIX_FMT_YUV420P,
            SWS_BILINEAR, nullptr, nullptr, nullptr);

        if (!recordSwsContext) {
            qDebug() << "无法创建录像转换上下文: 源" << frame->width << "x" << frame->height
                     << "目标" << m_recordCodecContext->width << "x" << m_recordCodecContext->height;
            return false;
        }

        qDebug() << "录像转换上下文创建成功";
    }

    // 验证转换前的数据
    if (!frame->data[0] || frame->linesize[0] <= 0) {
        qDebug() << "RGB帧数据无效: data[0]=" << (void*)frame->data[0] << "linesize[0]=" << frame->linesize[0];
        return false;
    }

    // 转换RGB帧到YUV420P
    int result = sws_scale(recordSwsContext, (const uint8_t* const*)frame->data, frame->linesize,
                          0, frame->height, yuvFrame->data, yuvFrame->linesize);

    if (result <= 0) {
        qDebug() << "像素格式转换失败: result=" << result;
        return false;
    }

    // 设置帧时间戳 - 关键修复：基于实际时间计算PTS
    // 计算从录像开始到现在的时间（秒）
    auto currentTime = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - startTime);
    double elapsedSeconds = elapsed.count() / 1000.0;

    // 根据10FPS的时间基计算PTS：PTS = 时间(秒) * time_base.den
    int64_t calculatedPTS = static_cast<int64_t>(elapsedSeconds * 10);

    // 使用frameIndex作为备用，确保PTS单调递增
    yuvFrame->pts = std::max(calculatedPTS, frameIndex);

    qDebug() << "帧时间戳: frameIndex=" << frameIndex
             << "elapsed=" << elapsedSeconds << "s"
             << "calculatedPTS=" << calculatedPTS
             << "finalPTS=" << yuvFrame->pts;

    frameIndex++;

    // 发送帧到编码器
    int ret = avcodec_send_frame(m_recordCodecContext, yuvFrame);
    if (ret < 0) {
        qDebug() << "发送帧到编码器失败:" << ret;
        return false;
    }

    // 接收编码后的数据包
    AVPacket *packet = av_packet_alloc();
    if (!packet) {
        qDebug() << "无法分配数据包";
        return false;
    }

    while (ret >= 0) {
        ret = avcodec_receive_packet(m_recordCodecContext, packet);
        if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF) {
            break;
        } else if (ret < 0) {
            qDebug() << "编码帧失败:" << ret;
            av_packet_free(&packet);
            return false;
        }

        // 设置数据包时间戳
        av_packet_rescale_ts(packet, m_recordCodecContext->time_base, m_recordVideoStream->time_base);
        packet->stream_index = m_recordVideoStream->index;

        // 写入数据包到文件
        ret = av_interleaved_write_frame(m_recordFormatContext, packet);
        if (ret < 0) {
            qDebug() << "写入帧失败:" << ret;
            av_packet_free(&packet);
            return false;
        }

        av_packet_unref(packet);
    }

    av_packet_free(&packet);
    return true;
}

void MainWindow::finalizeRecording()
{
    if (m_recordingInitialized && m_recordFormatContext) {
        // 写入文件尾
        av_write_trailer(m_recordFormatContext);

        // 关闭输出文件
        if (!(m_recordFormatContext->oformat->flags & AVFMT_NOFILE)) {
            avio_closep(&m_recordFormatContext->pb);
        }
    }

    // 清理录像资源
    if (m_recordCodecContext) {
        avcodec_free_context(&m_recordCodecContext);
    }

    if (m_recordFormatContext) {
        avformat_free_context(m_recordFormatContext);
        m_recordFormatContext = nullptr;
    }

    m_recordVideoStream = nullptr;
    m_recordCodec = nullptr;
    m_recordingInitialized = false;

    qDebug() << "录像资源清理完成";
}

// 添加静态变量清理函数
void MainWindow::cleanupRecordingStaticResources()
{
    qDebug() << "触发录像静态资源重置";

    // 重置updateVideoFrame中的静态变量
    // 这些变量在函数内部，通过下次调用时的逻辑重置

    // 设置全局标志，下次调用writeVideoFrame时会自动重置所有静态变量
    // 包括时间戳相关的变量
}