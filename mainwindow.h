#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QTimer>
#include <QPixmap>
#include <QLabel>
#include <QPushButton>
#include <QLineEdit>
#include <QMessageBox>
#include <QProcess>
#include <QDebug>
#include <QDateTime>
#include <QStandardPaths>
#include <QDir>
#include <QInputDialog>
#include <QApplication>
#include <QClipboard>
#include <QMutex>
#include <QThread>
#include <chrono>
#include <QTouchEvent>
#include <QKeyEvent>
#include <QEvent>
#include <QShortcut>

// FFmpeg C API头文件
extern "C" {
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libavutil/avutil.h>
#include <libavutil/imgutils.h>
#include <libswscale/swscale.h>
}

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void on_connectButton_clicked();
    void on_disconnectButton_clicked();
    void on_captureButton_clicked();
    void on_recordButton_clicked();
    void onFFmpegFinished(int exitCode, QProcess::ExitStatus exitStatus);
    void onFFmpegError(QProcess::ProcessError error);
    void onFFmpegErrorOutput();
    void onFFmpegStandardOutput();


private:
    Ui::MainWindow *ui;
    QTimer *m_frameTimer;
    bool m_isConnected;
    QProcess *m_ffmpegProcess;  // 保留用于网络配置
    QString m_currentRtspUrl;
    QString m_captureImagePath;
    QString m_recordVideoPath;
    bool m_isRecording;

    // FFmpeg相关成员变量
    AVFormatContext *m_formatContext;
    AVCodecContext *m_codecContext;
    const AVCodec *m_codec;
    AVFrame *m_frame;
    AVFrame *m_frameRGB;
    AVPacket *m_packet;
    struct SwsContext *m_swsContext;
    uint8_t *m_buffer;
    int m_videoStreamIndex;
    QMutex m_frameMutex;

    // 录像相关FFmpeg变量
    AVFormatContext *m_recordFormatContext;
    AVCodecContext *m_recordCodecContext;
    AVStream *m_recordVideoStream;
    const AVCodec *m_recordCodec;
    bool m_recordingInitialized;
    int m_recordFrameCount; // 录像帧计数器

    void setupUI();
    void setupTouchOptimization();      // 新增：触摸屏优化
    void setupSerialDebugSupport();     // 新增：串口调试支持
    void updateConnectionState(bool connected);
    void startRTSPStream(const QString &rtspUrl);
    void stopRTSPStream();
    void cleanupFFmpeg();
    void updateVideoFrame();
    bool isFFmpegAvailable();
    QString extractHostFromRtspUrl(const QString &rtspUrl);
    bool checkNetworkConnectivity(const QString &host);
    void performNetworkDiagnostics(const QString &host);

    // 新增：触摸和事件处理
    bool event(QEvent *event) override;
    void handleTouchEvent(QTouchEvent *touchEvent);
    void handleKeyboardInput(QKeyEvent *keyEvent);

    // FFmpeg核心功能函数
    bool initializeFFmpeg(const QString &rtspUrl);
    void cleanupFFmpegResources();
    bool readNextFrame();
    QPixmap convertFrameToPixmap(AVFrame *frame);

    // 拍照和录像功能
    void captureCurrentFrame();
    void startRecording();
    void stopRecording();
    void updateRecordingState(bool recording);
    QString generateCaptureFileName();
    QString generateRecordFileName();

    // FFmpeg录像功能
    bool initializeRecording(const QString &filename);
    bool writeVideoFrame(AVFrame *frame);
    void finalizeRecording();
    void cleanupRecordingStaticResources();


};

#endif // MAINWINDOW_H
