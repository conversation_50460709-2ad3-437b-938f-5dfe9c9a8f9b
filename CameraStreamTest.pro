QT       += core gui network

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++11

# s5p6818兼容版本 - GLIBC 2.18兼容基础UI版本
TARGET = s5p6818-qt-glibc218-compatible-arm

# You can make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    main.cpp \
    mainwindow.cpp

HEADERS += \
    mainwindow.h

FORMS += \
    mainwindow.ui

# FFmpeg库配置
# 检测是否为交叉编译环境 - 使用命令行参数
CONFIG(arm_cross_compile) {
    # ARM交叉编译配置
    message("使用ARM交叉编译配置 - FFmpeg 5.0.1")
    include(arm-cross-compile.pri)
} else {
    # 主机编译配置 - 使用pkg-config
    message("使用主机编译配置")
    CONFIG += link_pkgconfig
    PKGCONFIG += libavformat libavcodec libavutil libswscale libavfilter libavdevice

    # 额外的系统库依赖
    LIBS += -lz -lm -lpthread -ldl
}

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target
