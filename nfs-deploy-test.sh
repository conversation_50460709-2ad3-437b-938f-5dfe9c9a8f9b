#!/bin/bash

echo "=== s5p6818开发板NFS部署测试脚本 ==="

# 配置参数
NFS_ROOT="/home/<USER>/nfs_root"
APP_DIR="$NFS_ROOT/s5p6818-app"
BOARD_IP="*************"  # 请根据实际开发板IP修改
BOARD_USER="root"

echo "NFS根目录: $NFS_ROOT"
echo "应用目录: $APP_DIR"
echo "开发板IP: $BOARD_IP"

# 检查NFS服务状态
echo -e "\n=== 检查NFS服务状态 ==="
if systemctl is-active --quiet nfs-kernel-server; then
    echo "✅ NFS服务正在运行"
else
    echo "❌ NFS服务未运行，尝试启动..."
    sudo systemctl start nfs-kernel-server
    if [ $? -eq 0 ]; then
        echo "✅ NFS服务启动成功"
    else
        echo "❌ NFS服务启动失败，请检查配置"
        exit 1
    fi
fi

# 检查NFS导出配置
echo -e "\n=== 检查NFS导出配置 ==="
if grep -q "$NFS_ROOT" /etc/exports; then
    echo "✅ NFS导出配置已存在"
    showmount -e localhost | grep "$NFS_ROOT"
else
    echo "❌ NFS导出配置不存在，需要手动配置"
    echo "请在/etc/exports中添加以下行："
    echo "$NFS_ROOT *(rw,sync,no_subtree_check,no_root_squash)"
    echo "然后执行: sudo exportfs -ra"
    exit 1
fi

# 检查部署包完整性
echo -e "\n=== 检查部署包完整性 ==="
if [ -f "$APP_DIR/bin/s5p6818-qt-glibc218-compatible-arm" ]; then
    echo "✅ ARM可执行文件存在"
    ls -la "$APP_DIR/bin/s5p6818-qt-glibc218-compatible-arm"
else
    echo "❌ ARM可执行文件不存在"
    exit 1
fi

if [ -d "$APP_DIR/ffmpeg-lib" ]; then
    echo "✅ FFmpeg库目录存在"
    echo "FFmpeg库文件数量: $(ls $APP_DIR/ffmpeg-lib/*.so.* 2>/dev/null | wc -l)"
else
    echo "❌ FFmpeg库目录不存在"
    exit 1
fi

if [ -f "$APP_DIR/deploy.sh" ]; then
    echo "✅ 部署脚本存在"
else
    echo "❌ 部署脚本不存在"
    exit 1
fi

# 创建开发板连接测试脚本
echo -e "\n=== 创建开发板连接测试脚本 ==="
cat > test-board-connection.sh << 'EOF'
#!/bin/bash
BOARD_IP="*************"  # 请根据实际IP修改
BOARD_USER="root"

echo "=== 测试开发板连接 ==="
echo "尝试连接到 $BOARD_USER@$BOARD_IP"

# 测试SSH连接
if timeout 10 ssh -o ConnectTimeout=5 -o StrictHostKeyChecking=no $BOARD_USER@$BOARD_IP "echo 'SSH连接成功'" 2>/dev/null; then
    echo "✅ SSH连接正常"
    
    # 测试NFS挂载
    echo "测试NFS挂载..."
    ssh -o StrictHostKeyChecking=no $BOARD_USER@$BOARD_IP "
        mkdir -p /mnt/nfs
        if mount -t nfs $(hostname -I | awk '{print $1}'):/home/<USER>/nfs_root /mnt/nfs 2>/dev/null; then
            echo '✅ NFS挂载成功'
            ls -la /mnt/nfs/s5p6818-app/
            umount /mnt/nfs
        else
            echo '❌ NFS挂载失败'
        fi
    "
else
    echo "❌ 无法连接到开发板"
    echo "请检查："
    echo "1. 开发板IP地址是否正确"
    echo "2. 网络连接是否正常"
    echo "3. SSH服务是否启动"
fi
EOF

chmod +x test-board-connection.sh

echo -e "\n=== NFS部署测试准备完成 ==="
echo "下一步操作："
echo "1. 修改开发板IP地址: 编辑 test-board-connection.sh"
echo "2. 测试开发板连接: ./test-board-connection.sh"
echo "3. 如果连接正常，在开发板上执行:"
echo "   mkdir -p /mnt/nfs"
echo "   mount -t nfs <主机IP>:/home/<USER>/nfs_root /mnt/nfs"
echo "   cd /mnt/nfs/s5p6818-app"
echo "   ./deploy.sh"
echo "   /home/<USER>/s5p6818-app/start.sh"

echo -e "\n=== 部署包信息 ==="
echo "部署包大小: $(du -sh $APP_DIR | cut -f1)"
echo "可执行文件: $(file $APP_DIR/bin/s5p6818-qt-glibc218-compatible-arm | cut -d: -f2-)"
