# ARM交叉编译配置文件
# 用于s5p6818开发板的Qt应用程序交叉编译

# 设置交叉编译工具链
QMAKE_CC = arm-linux-gnueabihf-gcc
QMAKE_CXX = arm-linux-gnueabihf-g++
QMAKE_LINK = arm-linux-gnueabihf-g++
QMAKE_AR = arm-linux-gnueabihf-ar
QMAKE_STRIP = arm-linux-gnueabihf-strip

# 清除默认的Qt库路径
QT_LIBS =

# ARM Qt库路径
QT_ARM_PATH = /usr/lib/arm-linux-gnueabihf
LIBS += -L$$QT_ARM_PATH

# ARM Qt头文件路径
INCLUDEPATH += /usr/include/arm-linux-gnueabihf/qt5
INCLUDEPATH += /usr/include/arm-linux-gnueabihf/qt5/QtWidgets
INCLUDEPATH += /usr/include/arm-linux-gnueabihf/qt5/QtGui
INCLUDEPATH += /usr/include/arm-linux-gnueabihf/qt5/QtNetwork
INCLUDEPATH += /usr/include/arm-linux-gnueabihf/qt5/QtCore

# 手动指定ARM版本的Qt库
LIBS += $$QT_ARM_PATH/libQt5Widgets.so.5
LIBS += $$QT_ARM_PATH/libQt5Gui.so.5
LIBS += $$QT_ARM_PATH/libQt5Network.so.5
LIBS += $$QT_ARM_PATH/libQt5Core.so.5

# FFmpeg 5.0.1 ARM库配置
FFMPEG_PATH = /home/<USER>/arm-cross-compile/install/ffmpeg-arm
INCLUDEPATH += $$FFMPEG_PATH/include
LIBS += -L$$FFMPEG_PATH/lib
LIBS += -lavformat -lavcodec -lavutil -lswscale -lavfilter -lavdevice -lswresample

# 系统库依赖
LIBS += /usr/lib/arm-linux-gnueabihf/libz.so.1 -lm -lpthread -ldl

# ARM优化编译标志
QMAKE_CXXFLAGS += -march=armv7-a -mfpu=neon -mfloat-abi=hard
QMAKE_CXXFLAGS += -D_GLIBCXX_USE_CXX11_ABI=0
QMAKE_LFLAGS += -Wl,--as-needed

# 设置运行时库搜索路径
QMAKE_LFLAGS += -Wl,-rpath,/usr/lib/arm-linux-gnueabihf
QMAKE_LFLAGS += -Wl,-rpath,$$FFMPEG_PATH/lib

# 禁用默认的Qt库自动链接
CONFIG -= link_pkgconfig
